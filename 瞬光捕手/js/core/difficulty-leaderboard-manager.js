/**
 * 瞬光捕手 - 难度分类排行榜管理器
 * 扩展原有排行榜系统，支持按难度等级分类的排行榜
 */

class DifficultyLeaderboardManager {
    constructor() {
        this.initialized = false;
        this.difficultyLeaderboards = new Map();
        
        // 难度等级定义
        this.difficultyLevels = ['beginner', 'easy', 'normal', 'hard', 'expert', 'master'];
        
        // 排行榜类型（每个难度都有独立的排行榜）
        this.leaderboardCategories = {
            HIGH_SCORE: 'high_score',           // 最高分
            DAILY_HIGH_SCORE: 'daily_high_score', // 每日最高分
            WEEKLY_HIGH_SCORE: 'weekly_high_score', // 每周最高分
            MONTHLY_HIGH_SCORE: 'monthly_high_score', // 每月最高分
            PERFECT_HITS: 'perfect_hits',       // 完美击中
            COMBO_RECORD: 'combo_record',       // 连击记录
            LEVEL_COMPLETION: 'level_completion' // 关卡通关
        };
        
        // 配置参数
        this.config = {
            maxEntries: 100,                    // 每个排行榜最大条目数
            enableCrossDifficultyComparison: true, // 启用跨难度比较
            difficultyWeights: {                // 难度权重（用于跨难度比较）
                beginner: 0.6,
                easy: 0.8,
                normal: 1.0,
                hard: 1.3,
                expert: 1.6,
                master: 2.0
            },
            resetSchedule: {
                daily: { hour: 0, minute: 0 },
                weekly: { day: 1, hour: 0, minute: 0 }, // 周一
                monthly: { day: 1, hour: 0, minute: 0 }  // 每月1日
            }
        };
        
        console.log('🏆 难度分类排行榜管理器已创建');
    }

    /**
     * 初始化难度排行榜管理器
     */
    async init() {
        try {
            // 确保难度配置管理器已初始化
            if (window.difficultyConfigManager && !window.difficultyConfigManager.initialized) {
                await difficultyConfigManager.init();
            }
            
            // 加载所有难度排行榜数据
            await this.loadDifficultyLeaderboards();
            
            // 检查并重置过期的排行榜
            await this.checkAndResetExpiredLeaderboards();
            
            // 确保原有排行榜管理器已初始化
            if (window.leaderboardManager && !window.leaderboardManager.initialized) {
                await leaderboardManager.init();
            }
            
            this.initialized = true;
            console.log('✅ 难度分类排行榜管理器初始化完成');
            return true;
        } catch (error) {
            console.error('❌ 难度分类排行榜管理器初始化失败:', error);
            this.initialized = true; // 即使失败也标记为已初始化
            return false;
        }
    }

    /**
     * 加载所有难度排行榜数据
     */
    async loadDifficultyLeaderboards() {
        try {
            if (!window.storageService) {
                console.warn('⚠️ 存储服务不可用，使用内存存储');
                return;
            }

            // 为每个难度等级加载排行榜数据
            for (const difficulty of this.difficultyLevels) {
                const difficultyData = new Map();
                
                for (const category of Object.values(this.leaderboardCategories)) {
                    const key = `difficulty_leaderboard.${difficulty}.${category}`;
                    const leaderboardData = await storageService.get(key, null);
                    
                    if (leaderboardData) {
                        difficultyData.set(category, leaderboardData);
                    } else {
                        // 创建空的排行榜
                        const emptyLeaderboard = this.createEmptyLeaderboard(difficulty, category);
                        difficultyData.set(category, emptyLeaderboard);
                    }
                }
                
                this.difficultyLeaderboards.set(difficulty, difficultyData);
            }
            
            console.log(`📊 加载了 ${this.difficultyLevels.length} 个难度等级的排行榜数据`);
        } catch (error) {
            console.error('❌ 加载难度排行榜数据失败:', error);
        }
    }

    /**
     * 创建空的排行榜
     */
    createEmptyLeaderboard(difficulty, category) {
        return {
            difficulty: difficulty,
            category: category,
            type: `${difficulty}_${category}`,
            entries: [],
            lastReset: Date.now(),
            createdAt: Date.now(),
            metadata: {
                totalSubmissions: 0,
                uniquePlayers: 0,
                averageScore: 0
            }
        };
    }

    /**
     * 提交分数到难度排行榜
     * @param {string} difficulty - 难度等级
     * @param {string} category - 排行榜类别
     * @param {Object} scoreData - 分数数据
     * @returns {Promise<Object>} 提交结果
     */
    async submitScore(difficulty, category, scoreData) {
        try {
            if (!this.initialized) {
                throw new Error('难度排行榜管理器未初始化');
            }

            // 验证难度等级
            if (!this.difficultyLevels.includes(difficulty)) {
                throw new Error(`无效的难度等级: ${difficulty}`);
            }

            // 验证排行榜类别
            if (!Object.values(this.leaderboardCategories).includes(category)) {
                throw new Error(`无效的排行榜类别: ${category}`);
            }

            // 验证分数数据
            const validationResult = this.validateScoreData(scoreData, difficulty);
            if (!validationResult.valid) {
                throw new Error(`分数数据验证失败: ${validationResult.reason}`);
            }

            // 获取对应的排行榜
            const difficultyData = this.difficultyLeaderboards.get(difficulty);
            const leaderboard = difficultyData.get(category);

            // 创建排行榜条目
            const entry = this.createLeaderboardEntry(scoreData, difficulty);

            // 添加到排行榜
            leaderboard.entries.push(entry);

            // 排序并限制条目数量
            this.sortAndLimitLeaderboard(leaderboard, category);

            // 更新元数据
            this.updateLeaderboardMetadata(leaderboard, entry);

            // 保存排行榜数据
            await this.saveLeaderboard(difficulty, category, leaderboard);

            // 计算玩家排名
            const playerRank = this.calculatePlayerRank(leaderboard, entry.playerId);

            console.log(`🏆 分数已提交到 ${difficulty} 难度的 ${category} 排行榜`);

            return {
                success: true,
                difficulty: difficulty,
                category: category,
                rank: playerRank.rank,
                totalEntries: playerRank.totalEntries,
                isNewRecord: playerRank.rank === 1,
                entry: entry
            };

        } catch (error) {
            console.error('❌ 提交分数到难度排行榜失败:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 验证分数数据
     * @param {Object} scoreData - 分数数据
     * @param {string} difficulty - 难度等级
     * @returns {Object} 验证结果
     */
    validateScoreData(scoreData, difficulty) {
        // 基础字段验证
        const requiredFields = ['score', 'playerId', 'playerName'];
        for (const field of requiredFields) {
            if (!scoreData.hasOwnProperty(field)) {
                return { valid: false, reason: `缺少必需字段: ${field}` };
            }
        }

        // 分数合理性验证
        if (typeof scoreData.score !== 'number' || scoreData.score < 0) {
            return { valid: false, reason: '分数必须是非负数' };
        }

        // 如果有防作弊系统，进行额外验证
        if (window.antiCheatSystem && window.antiCheatSystem.initialized) {
            const cheatCheck = antiCheatSystem.validateScore(scoreData);
            if (!cheatCheck.valid) {
                return { valid: false, reason: `防作弊验证失败: ${cheatCheck.reason}` };
            }
        }

        // 难度特定验证
        if (window.difficultyConfigManager && window.difficultyConfigManager.initialized) {
            const difficultyConfig = difficultyConfigManager.getGameDifficultyConfig('spark-catcher', difficulty);
            
            // 验证分数是否在合理范围内
            const maxReasonableScore = difficultyConfig.level.targetScore * 5; // 目标分数的5倍作为上限
            if (scoreData.score > maxReasonableScore) {
                return { valid: false, reason: `分数超出合理范围 (最大: ${maxReasonableScore})` };
            }
        }

        return { valid: true };
    }

    /**
     * 创建排行榜条目
     * @param {Object} scoreData - 分数数据
     * @param {string} difficulty - 难度等级
     * @returns {Object} 排行榜条目
     */
    createLeaderboardEntry(scoreData, difficulty) {
        return {
            playerId: scoreData.playerId,
            playerName: scoreData.playerName,
            score: scoreData.score,
            difficulty: difficulty,
            level: scoreData.level || 1,
            perfectHits: scoreData.perfectHits || 0,
            combo: scoreData.combo || 0,
            accuracy: scoreData.accuracy || 0,
            timestamp: Date.now(),
            gameData: {
                duration: scoreData.duration || 0,
                totalHits: scoreData.totalHits || 0,
                missedHits: scoreData.missedHits || 0,
                difficultyMultiplier: this.config.difficultyWeights[difficulty] || 1.0
            },
            metadata: {
                submissionId: this.generateSubmissionId(),
                clientVersion: scoreData.clientVersion || 'unknown',
                deviceInfo: scoreData.deviceInfo || {}
            }
        };
    }

    /**
     * 排序并限制排行榜条目
     * @param {Object} leaderboard - 排行榜对象
     * @param {string} category - 排行榜类别
     */
    sortAndLimitLeaderboard(leaderboard, category) {
        // 根据类别进行排序
        switch (category) {
            case this.leaderboardCategories.HIGH_SCORE:
            case this.leaderboardCategories.DAILY_HIGH_SCORE:
            case this.leaderboardCategories.WEEKLY_HIGH_SCORE:
            case this.leaderboardCategories.MONTHLY_HIGH_SCORE:
                leaderboard.entries.sort((a, b) => b.score - a.score);
                break;
                
            case this.leaderboardCategories.PERFECT_HITS:
                leaderboard.entries.sort((a, b) => b.perfectHits - a.perfectHits);
                break;
                
            case this.leaderboardCategories.COMBO_RECORD:
                leaderboard.entries.sort((a, b) => b.combo - a.combo);
                break;
                
            case this.leaderboardCategories.LEVEL_COMPLETION:
                leaderboard.entries.sort((a, b) => {
                    if (b.level !== a.level) {
                        return b.level - a.level;
                    }
                    return b.score - a.score; // 相同关卡按分数排序
                });
                break;
        }

        // 限制条目数量
        leaderboard.entries = leaderboard.entries.slice(0, this.config.maxEntries);

        // 移除重复的玩家条目（保留最高分）
        const uniqueEntries = new Map();
        leaderboard.entries.forEach(entry => {
            const existingEntry = uniqueEntries.get(entry.playerId);
            if (!existingEntry || this.compareEntries(entry, existingEntry, category) > 0) {
                uniqueEntries.set(entry.playerId, entry);
            }
        });

        leaderboard.entries = Array.from(uniqueEntries.values());
        
        // 重新排序
        this.sortAndLimitLeaderboard(leaderboard, category);
    }

    /**
     * 比较两个排行榜条目
     * @param {Object} entry1 - 条目1
     * @param {Object} entry2 - 条目2
     * @param {string} category - 排行榜类别
     * @returns {number} 比较结果 (1: entry1更好, -1: entry2更好, 0: 相等)
     */
    compareEntries(entry1, entry2, category) {
        switch (category) {
            case this.leaderboardCategories.HIGH_SCORE:
            case this.leaderboardCategories.DAILY_HIGH_SCORE:
            case this.leaderboardCategories.WEEKLY_HIGH_SCORE:
            case this.leaderboardCategories.MONTHLY_HIGH_SCORE:
                return entry1.score > entry2.score ? 1 : (entry1.score < entry2.score ? -1 : 0);
                
            case this.leaderboardCategories.PERFECT_HITS:
                return entry1.perfectHits > entry2.perfectHits ? 1 : (entry1.perfectHits < entry2.perfectHits ? -1 : 0);
                
            case this.leaderboardCategories.COMBO_RECORD:
                return entry1.combo > entry2.combo ? 1 : (entry1.combo < entry2.combo ? -1 : 0);
                
            case this.leaderboardCategories.LEVEL_COMPLETION:
                if (entry1.level !== entry2.level) {
                    return entry1.level > entry2.level ? 1 : -1;
                }
                return entry1.score > entry2.score ? 1 : (entry1.score < entry2.score ? -1 : 0);
                
            default:
                return 0;
        }
    }

    /**
     * 更新排行榜元数据
     * @param {Object} leaderboard - 排行榜对象
     * @param {Object} entry - 新条目
     */
    updateLeaderboardMetadata(leaderboard, entry) {
        leaderboard.metadata.totalSubmissions++;
        leaderboard.metadata.lastUpdated = Date.now();
        
        // 计算唯一玩家数量
        const uniquePlayers = new Set(leaderboard.entries.map(e => e.playerId));
        leaderboard.metadata.uniquePlayers = uniquePlayers.size;
        
        // 计算平均分数
        if (leaderboard.entries.length > 0) {
            const totalScore = leaderboard.entries.reduce((sum, e) => sum + e.score, 0);
            leaderboard.metadata.averageScore = Math.round(totalScore / leaderboard.entries.length);
        }
    }

    /**
     * 计算玩家排名
     * @param {Object} leaderboard - 排行榜对象
     * @param {string} playerId - 玩家ID
     * @returns {Object} 排名信息
     */
    calculatePlayerRank(leaderboard, playerId) {
        const playerIndex = leaderboard.entries.findIndex(entry => entry.playerId === playerId);
        
        if (playerIndex === -1) {
            return {
                rank: null,
                totalEntries: leaderboard.entries.length,
                entry: null
            };
        }
        
        return {
            rank: playerIndex + 1,
            totalEntries: leaderboard.entries.length,
            entry: leaderboard.entries[playerIndex]
        };
    }

    /**
     * 获取指定难度和类别的排行榜
     * @param {string} difficulty - 难度等级
     * @param {string} category - 排行榜类别
     * @param {number} limit - 返回条目数量限制
     * @returns {Object} 排行榜数据
     */
    getLeaderboard(difficulty, category, limit = 20) {
        try {
            if (!this.initialized) {
                console.warn('⚠️ 难度排行榜管理器未初始化');
                return null;
            }

            const difficultyData = this.difficultyLeaderboards.get(difficulty);
            if (!difficultyData) {
                console.warn(`⚠️ 未找到难度 ${difficulty} 的排行榜数据`);
                return null;
            }

            const leaderboard = difficultyData.get(category);
            if (!leaderboard) {
                console.warn(`⚠️ 未找到类别 ${category} 的排行榜数据`);
                return null;
            }

            return {
                ...leaderboard,
                entries: leaderboard.entries.slice(0, limit)
            };
        } catch (error) {
            console.error('❌ 获取难度排行榜失败:', error);
            return null;
        }
    }

    /**
     * 获取玩家在指定难度排行榜中的排名
     * @param {string} difficulty - 难度等级
     * @param {string} category - 排行榜类别
     * @param {string} playerId - 玩家ID
     * @returns {Object} 玩家排名信息
     */
    getPlayerRank(difficulty, category, playerId) {
        try {
            const leaderboard = this.getLeaderboard(difficulty, category, this.config.maxEntries);
            if (!leaderboard) {
                return null;
            }

            return this.calculatePlayerRank(leaderboard, playerId);
        } catch (error) {
            console.error('❌ 获取玩家排名失败:', error);
            return null;
        }
    }

    /**
     * 保存排行榜数据
     * @param {string} difficulty - 难度等级
     * @param {string} category - 排行榜类别
     * @param {Object} leaderboard - 排行榜数据
     */
    async saveLeaderboard(difficulty, category, leaderboard) {
        try {
            if (!window.storageService) {
                console.warn('⚠️ 存储服务不可用，无法保存排行榜数据');
                return;
            }

            const key = `difficulty_leaderboard.${difficulty}.${category}`;
            await window.storageService.set(key, leaderboard);
        } catch (error) {
            console.error('❌ 保存排行榜数据失败:', error);
        }
    }

    /**
     * 生成提交ID
     * @returns {string} 唯一的提交ID
     */
    generateSubmissionId() {
        return `sub_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 检查并重置过期的排行榜
     */
    async checkAndResetExpiredLeaderboards() {
        const now = new Date();
        
        for (const difficulty of this.difficultyLevels) {
            // 检查每日排行榜
            await this.checkDailyReset(difficulty, now);
            
            // 检查每周排行榜
            await this.checkWeeklyReset(difficulty, now);
            
            // 检查每月排行榜
            await this.checkMonthlyReset(difficulty, now);
        }
    }

    /**
     * 检查每日排行榜重置
     */
    async checkDailyReset(difficulty, now) {
        const category = this.leaderboardCategories.DAILY_HIGH_SCORE;
        const leaderboard = this.getLeaderboard(difficulty, category, this.config.maxEntries);
        
        if (!leaderboard) return;
        
        const lastReset = new Date(leaderboard.lastReset);
        const shouldReset = now.getDate() !== lastReset.getDate() || 
                          now.getMonth() !== lastReset.getMonth() || 
                          now.getFullYear() !== lastReset.getFullYear();
        
        if (shouldReset) {
            await this.resetLeaderboard(difficulty, category);
            console.log(`📊 ${difficulty} 难度每日排行榜已重置`);
        }
    }

    /**
     * 检查每周排行榜重置
     */
    async checkWeeklyReset(difficulty, now) {
        const category = this.leaderboardCategories.WEEKLY_HIGH_SCORE;
        const leaderboard = this.getLeaderboard(difficulty, category, this.config.maxEntries);
        
        if (!leaderboard) return;
        
        const lastReset = new Date(leaderboard.lastReset);
        const weeksDiff = Math.floor((now - lastReset) / (7 * 24 * 60 * 60 * 1000));
        
        if (weeksDiff >= 1 && now.getDay() === this.config.resetSchedule.weekly.day) {
            await this.resetLeaderboard(difficulty, category);
            console.log(`📊 ${difficulty} 难度每周排行榜已重置`);
        }
    }

    /**
     * 检查每月排行榜重置
     */
    async checkMonthlyReset(difficulty, now) {
        const category = this.leaderboardCategories.MONTHLY_HIGH_SCORE;
        const leaderboard = this.getLeaderboard(difficulty, category, this.config.maxEntries);
        
        if (!leaderboard) return;
        
        const lastReset = new Date(leaderboard.lastReset);
        const shouldReset = now.getMonth() !== lastReset.getMonth() || 
                          now.getFullYear() !== lastReset.getFullYear();
        
        if (shouldReset && now.getDate() === this.config.resetSchedule.monthly.day) {
            await this.resetLeaderboard(difficulty, category);
            console.log(`📊 ${difficulty} 难度每月排行榜已重置`);
        }
    }

    /**
     * 重置指定排行榜
     * @param {string} difficulty - 难度等级
     * @param {string} category - 排行榜类别
     */
    async resetLeaderboard(difficulty, category) {
        try {
            const difficultyData = this.difficultyLeaderboards.get(difficulty);
            const leaderboard = difficultyData.get(category);
            
            // 备份当前排行榜
            await this.backupLeaderboard(leaderboard);
            
            // 创建新的空排行榜
            const newLeaderboard = this.createEmptyLeaderboard(difficulty, category);
            difficultyData.set(category, newLeaderboard);
            
            // 保存重置后的排行榜
            await this.saveLeaderboard(difficulty, category, newLeaderboard);
            
            console.log(`🔄 ${difficulty} 难度的 ${category} 排行榜已重置`);
        } catch (error) {
            console.error('❌ 重置排行榜失败:', error);
        }
    }

    /**
     * 备份排行榜数据
     * @param {Object} leaderboard - 排行榜数据
     */
    async backupLeaderboard(leaderboard) {
        try {
            if (!window.storageService) return;
            
            const backupKey = `difficulty_leaderboard_backup.${leaderboard.difficulty}.${leaderboard.category}.${Date.now()}`;
            await storageService.set(backupKey, leaderboard);
            
            console.log(`💾 排行榜已备份: ${backupKey}`);
        } catch (error) {
            console.error('❌ 备份排行榜失败:', error);
        }
    }

    /**
     * 获取所有支持的难度等级
     * @returns {Array} 难度等级数组
     */
    getSupportedDifficulties() {
        return [...this.difficultyLevels];
    }

    /**
     * 获取所有支持的排行榜类别
     * @returns {Object} 排行榜类别对象
     */
    getSupportedCategories() {
        return { ...this.leaderboardCategories };
    }

    /**
     * 获取难度权重
     * @param {string} difficulty - 难度等级
     * @returns {number} 难度权重
     */
    getDifficultyWeight(difficulty) {
        return this.config.difficultyWeights[difficulty] || 1.0;
    }

    /**
     * 获取跨难度综合排行榜
     * @param {string} category - 排行榜类别
     * @param {number} limit - 返回条目数量限制
     * @returns {Object} 综合排行榜数据
     */
    getCrossDifficultyLeaderboard(category, limit = 20) {
        try {
            if (!this.config.enableCrossDifficultyComparison) {
                console.warn('⚠️ 跨难度比较功能已禁用');
                return null;
            }

            const allEntries = [];

            // 收集所有难度的排行榜条目
            for (const difficulty of this.difficultyLevels) {
                const leaderboard = this.getLeaderboard(difficulty, category, this.config.maxEntries);
                if (leaderboard && leaderboard.entries.length > 0) {
                    // 计算加权分数
                    const weightedEntries = leaderboard.entries.map(entry => ({
                        ...entry,
                        originalScore: entry.score,
                        weightedScore: this.calculateWeightedScore(entry.score, difficulty, category),
                        difficultyWeight: this.getDifficultyWeight(difficulty)
                    }));

                    allEntries.push(...weightedEntries);
                }
            }

            // 按加权分数排序
            allEntries.sort((a, b) => b.weightedScore - a.weightedScore);

            // 限制条目数量
            const topEntries = allEntries.slice(0, limit);

            return {
                type: `cross_difficulty_${category}`,
                category: category,
                entries: topEntries,
                metadata: {
                    totalEntries: allEntries.length,
                    difficultiesIncluded: this.difficultyLevels,
                    weightingEnabled: true,
                    generatedAt: Date.now()
                }
            };
        } catch (error) {
            console.error('❌ 获取跨难度排行榜失败:', error);
            return null;
        }
    }

    /**
     * 计算加权分数
     * @param {number} score - 原始分数
     * @param {string} difficulty - 难度等级
     * @param {string} category - 排行榜类别
     * @returns {number} 加权分数
     */
    calculateWeightedScore(score, difficulty, category) {
        const weight = this.getDifficultyWeight(difficulty);

        // 根据排行榜类别调整加权算法
        switch (category) {
            case this.leaderboardCategories.HIGH_SCORE:
            case this.leaderboardCategories.DAILY_HIGH_SCORE:
            case this.leaderboardCategories.WEEKLY_HIGH_SCORE:
            case this.leaderboardCategories.MONTHLY_HIGH_SCORE:
                // 分数类排行榜：直接乘以权重
                return Math.round(score * weight);

            case this.leaderboardCategories.PERFECT_HITS:
            case this.leaderboardCategories.COMBO_RECORD:
                // 技能类排行榜：使用平方根权重以减少差距
                return Math.round(score * Math.sqrt(weight));

            case this.leaderboardCategories.LEVEL_COMPLETION:
                // 关卡完成类：权重影响较小
                return Math.round(score * (1 + (weight - 1) * 0.5));

            default:
                return Math.round(score * weight);
        }
    }

    /**
     * 获取玩家在所有难度中的最佳成绩
     * @param {string} playerId - 玩家ID
     * @param {string} category - 排行榜类别
     * @returns {Object} 玩家最佳成绩信息
     */
    getPlayerBestAcrossDifficulties(playerId, category) {
        try {
            let bestEntry = null;
            let bestDifficulty = null;
            let bestWeightedScore = 0;

            // 遍历所有难度等级
            for (const difficulty of this.difficultyLevels) {
                const playerRank = this.getPlayerRank(difficulty, category, playerId);

                if (playerRank && playerRank.entry) {
                    const weightedScore = this.calculateWeightedScore(
                        playerRank.entry.score,
                        difficulty,
                        category
                    );

                    if (weightedScore > bestWeightedScore) {
                        bestWeightedScore = weightedScore;
                        bestEntry = {
                            ...playerRank.entry,
                            weightedScore: weightedScore,
                            rank: playerRank.rank,
                            totalEntries: playerRank.totalEntries
                        };
                        bestDifficulty = difficulty;
                    }
                }
            }

            if (!bestEntry) {
                return null;
            }

            return {
                playerId: playerId,
                bestDifficulty: bestDifficulty,
                category: category,
                entry: bestEntry,
                allDifficultyRanks: this.getAllDifficultyRanks(playerId, category)
            };
        } catch (error) {
            console.error('❌ 获取玩家跨难度最佳成绩失败:', error);
            return null;
        }
    }

    /**
     * 获取玩家在所有难度中的排名
     * @param {string} playerId - 玩家ID
     * @param {string} category - 排行榜类别
     * @returns {Object} 所有难度的排名信息
     */
    getAllDifficultyRanks(playerId, category) {
        const ranks = {};

        for (const difficulty of this.difficultyLevels) {
            const playerRank = this.getPlayerRank(difficulty, category, playerId);
            ranks[difficulty] = playerRank;
        }

        return ranks;
    }

    /**
     * 获取难度统计信息
     * @param {string} difficulty - 难度等级
     * @returns {Object} 难度统计信息
     */
    getDifficultyStats(difficulty) {
        try {
            const difficultyData = this.difficultyLeaderboards.get(difficulty);
            if (!difficultyData) {
                return null;
            }

            const stats = {
                difficulty: difficulty,
                totalPlayers: 0,
                totalSubmissions: 0,
                categories: {}
            };

            const uniquePlayers = new Set();

            for (const [category, leaderboard] of difficultyData) {
                const categoryStats = {
                    category: category,
                    entries: leaderboard.entries.length,
                    topScore: leaderboard.entries.length > 0 ? leaderboard.entries[0].score : 0,
                    averageScore: leaderboard.metadata.averageScore || 0,
                    uniquePlayers: leaderboard.metadata.uniquePlayers || 0,
                    totalSubmissions: leaderboard.metadata.totalSubmissions || 0,
                    lastUpdated: leaderboard.metadata.lastUpdated || leaderboard.createdAt
                };

                stats.categories[category] = categoryStats;
                stats.totalSubmissions += categoryStats.totalSubmissions;

                // 收集唯一玩家
                leaderboard.entries.forEach(entry => {
                    uniquePlayers.add(entry.playerId);
                });
            }

            stats.totalPlayers = uniquePlayers.size;

            return stats;
        } catch (error) {
            console.error('❌ 获取难度统计信息失败:', error);
            return null;
        }
    }

    /**
     * 获取全局统计信息
     * @returns {Object} 全局统计信息
     */
    getGlobalStats() {
        try {
            const globalStats = {
                totalDifficulties: this.difficultyLevels.length,
                totalCategories: Object.keys(this.leaderboardCategories).length,
                totalPlayers: 0,
                totalSubmissions: 0,
                difficulties: {},
                popularDifficulty: null,
                popularCategory: null
            };

            const allPlayers = new Set();
            const difficultySubmissions = {};
            const categorySubmissions = {};

            // 收集所有难度的统计信息
            for (const difficulty of this.difficultyLevels) {
                const difficultyStats = this.getDifficultyStats(difficulty);
                if (difficultyStats) {
                    globalStats.difficulties[difficulty] = difficultyStats;
                    globalStats.totalSubmissions += difficultyStats.totalSubmissions;
                    difficultySubmissions[difficulty] = difficultyStats.totalSubmissions;

                    // 收集唯一玩家
                    const difficultyData = this.difficultyLeaderboards.get(difficulty);
                    for (const [category, leaderboard] of difficultyData) {
                        leaderboard.entries.forEach(entry => {
                            allPlayers.add(entry.playerId);
                        });

                        // 统计类别提交数
                        const submissions = leaderboard.metadata.totalSubmissions || 0;
                        categorySubmissions[category] = (categorySubmissions[category] || 0) + submissions;
                    }
                }
            }

            globalStats.totalPlayers = allPlayers.size;

            // 找出最受欢迎的难度和类别
            globalStats.popularDifficulty = Object.keys(difficultySubmissions).reduce((a, b) =>
                difficultySubmissions[a] > difficultySubmissions[b] ? a : b
            );

            globalStats.popularCategory = Object.keys(categorySubmissions).reduce((a, b) =>
                categorySubmissions[a] > categorySubmissions[b] ? a : b
            );

            return globalStats;
        } catch (error) {
            console.error('❌ 获取全局统计信息失败:', error);
            return null;
        }
    }

    /**
     * 清理过期的备份数据
     * @param {number} maxAge - 最大保留时间（毫秒）
     */
    async cleanupOldBackups(maxAge = 30 * 24 * 60 * 60 * 1000) { // 默认30天
        try {
            if (!window.storageService) return;

            const backupKeys = await storageService.list('difficulty_leaderboard_backup.');
            const now = Date.now();

            for (const key of backupKeys) {
                // 从键名中提取时间戳
                const timestampMatch = key.match(/\.(\d+)$/);
                if (timestampMatch) {
                    const timestamp = parseInt(timestampMatch[1]);
                    if (now - timestamp > maxAge) {
                        await storageService.remove(key);
                        console.log(`🗑️ 已清理过期备份: ${key}`);
                    }
                }
            }
        } catch (error) {
            console.error('❌ 清理过期备份失败:', error);
        }
    }

    /**
     * 导出排行榜数据
     * @param {string} difficulty - 难度等级（可选）
     * @param {string} category - 排行榜类别（可选）
     * @returns {Object} 导出的数据
     */
    exportLeaderboardData(difficulty = null, category = null) {
        try {
            const exportData = {
                exportedAt: Date.now(),
                version: '1.0',
                data: {}
            };

            if (difficulty && category) {
                // 导出特定难度和类别
                const leaderboard = this.getLeaderboard(difficulty, category, this.config.maxEntries);
                exportData.data[`${difficulty}_${category}`] = leaderboard;
            } else if (difficulty) {
                // 导出特定难度的所有类别
                const difficultyData = this.difficultyLeaderboards.get(difficulty);
                if (difficultyData) {
                    for (const [cat, leaderboard] of difficultyData) {
                        exportData.data[`${difficulty}_${cat}`] = leaderboard;
                    }
                }
            } else {
                // 导出所有数据
                for (const diff of this.difficultyLevels) {
                    const difficultyData = this.difficultyLeaderboards.get(diff);
                    if (difficultyData) {
                        for (const [cat, leaderboard] of difficultyData) {
                            exportData.data[`${diff}_${cat}`] = leaderboard;
                        }
                    }
                }
            }

            return exportData;
        } catch (error) {
            console.error('❌ 导出排行榜数据失败:', error);
            return null;
        }
    }

    /**
     * 导入排行榜数据
     * @param {Object} importData - 导入的数据
     * @returns {Promise<boolean>} 导入是否成功
     */
    async importLeaderboardData(importData) {
        try {
            if (!importData || !importData.data) {
                throw new Error('导入数据格式无效');
            }

            let importCount = 0;

            for (const [key, leaderboard] of Object.entries(importData.data)) {
                const [difficulty, category] = key.split('_');

                if (this.difficultyLevels.includes(difficulty) &&
                    Object.values(this.leaderboardCategories).includes(category)) {

                    // 验证排行榜数据
                    if (this.validateLeaderboardData(leaderboard)) {
                        const difficultyData = this.difficultyLeaderboards.get(difficulty);
                        difficultyData.set(category, leaderboard);

                        await this.saveLeaderboard(difficulty, category, leaderboard);
                        importCount++;
                    }
                }
            }

            console.log(`✅ 成功导入 ${importCount} 个排行榜`);
            return true;
        } catch (error) {
            console.error('❌ 导入排行榜数据失败:', error);
            return false;
        }
    }

    /**
     * 验证排行榜数据
     * @param {Object} leaderboard - 排行榜数据
     * @returns {boolean} 数据是否有效
     */
    validateLeaderboardData(leaderboard) {
        if (!leaderboard || typeof leaderboard !== 'object') {
            return false;
        }

        const requiredFields = ['difficulty', 'category', 'entries', 'metadata'];
        for (const field of requiredFields) {
            if (!leaderboard.hasOwnProperty(field)) {
                return false;
            }
        }

        if (!Array.isArray(leaderboard.entries)) {
            return false;
        }

        // 验证条目格式
        for (const entry of leaderboard.entries) {
            if (!entry.playerId || !entry.playerName || typeof entry.score !== 'number') {
                return false;
            }
        }

        return true;
    }
}

// 创建全局难度排行榜管理器实例
const difficultyLeaderboardManager = new DifficultyLeaderboardManager();

// 导出到全局作用域
if (typeof window !== 'undefined') {
    window.DifficultyLeaderboardManager = DifficultyLeaderboardManager;
    window.difficultyLeaderboardManager = difficultyLeaderboardManager;
}

// 支持CommonJS
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        DifficultyLeaderboardManager,
        difficultyLeaderboardManager
    };
}
